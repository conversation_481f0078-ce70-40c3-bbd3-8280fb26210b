using UnitParts.Interfaces;
using UnitParts.Orders;
using UnitParts.Orders.Decorators;

namespace Managers
{
    /// <summary>
    ///     Interface for OrderPoolManager to enable dependency injection
    /// </summary>
    public interface IOrderPoolManager
    {
        /// <summary>
        ///     Pool for move orders
        /// </summary>
        ObjectPool<MoveOrder> MoverOrderPool { get; }

        /// <summary>
        ///     Pool for attack move orders
        /// </summary>
        ObjectPool<AttackMoveOrder> AttackMoveOrderPool { get; }

        /// <summary>
        ///     Pool for stay still orders
        /// </summary>
        ObjectPool<StayStillOrder> StayStillOrderPool { get; }

        /// <summary>
        ///     Pool for defend orders
        /// </summary>
        ObjectPool<DefendOrder> DefendOrderPool { get; }

        /// <summary>
        ///     Pool for resource thief orders
        /// </summary>
        ObjectPool<ResourceThiefOrder> ResourceThiefOrderPool { get; }

        // Decorator pools - now with proper pooling support
        /// <summary>
        ///     Pool for TimedDecorator instances
        /// </summary>
        DecoratorPool<TimedDecorator> TimedDecoratorPool { get; }

        /// <summary>
        ///     Pool for TargetTrackingDecorator instances
        /// </summary>
        DecoratorPool<TargetTrackingDecorator> TargetTrackingDecoratorPool { get; }

        /// <summary>
        ///     Pool for GunTargetDecorator instances
        /// </summary>
        DecoratorPool<GunTargetDecorator> GunTargetDecoratorPool { get; }

        /// <summary>
        ///     Pool for EnemyDetectionDecorator instances
        /// </summary>
        DecoratorPool<EnemyDetectionDecorator> EnemyDetectionDecoratorPool { get; }

        /// <summary>
        ///     Pool for ConditionalDecorator instances
        /// </summary>
        DecoratorPool<ConditionalDecorator> ConditionalDecoratorPool { get; }

        // Decorator factory methods - now using pooling for better performance
        /// <summary>
        ///     Creates a pooled TimedDecorator instance. Uses object pooling to reduce GC pressure.
        /// </summary>
        TimedDecorator CreateTimedDecorator(IBaseOrder wrappedOrder);

        /// <summary>
        ///     Creates a pooled TargetTrackingDecorator instance. Uses object pooling to reduce GC pressure.
        /// </summary>
        TargetTrackingDecorator CreateTargetTrackingDecorator(IBaseOrder wrappedOrder);

        /// <summary>
        ///     Creates a pooled GunTargetDecorator instance. Uses object pooling to reduce GC pressure.
        /// </summary>
        GunTargetDecorator CreateGunTargetDecorator(IBaseOrder wrappedOrder);

        /// <summary>
        ///     Creates a pooled EnemyDetectionDecorator instance. Uses object pooling to reduce GC pressure.
        /// </summary>
        EnemyDetectionDecorator CreateEnemyDetectionDecorator(IBaseOrder wrappedOrder);

        /// <summary>
        ///     Creates a pooled ConditionalDecorator instance. Uses object pooling to reduce GC pressure.
        /// </summary>
        ConditionalDecorator CreateConditionalDecorator(IBaseOrder wrappedOrder);
    }
}
