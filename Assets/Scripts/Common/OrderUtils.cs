using Buildings;
using Managers;
using System;
using UnitParts.Controllers;
using UnitParts.Interfaces;
using UnitParts.Orders;
using UnitParts.Orders.Decorators;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Common
{
    /// <summary>
    /// Centralized factory for creating and managing all order types with proper pooling and documentation.
    /// This class provides static factory methods for creating orders with proper parameter validation,
    /// default value assignment, and comprehensive documentation for all order types and their behaviors.
    /// </summary>
    public abstract class OrderUtils
    {
        /// <summary>
        /// Creates a direct attack order that moves the unit to an optimal attack position and engages the target.
        /// The unit will move to a calculated optimal distance from the target based on gun range, then attack.
        /// If the target is destroyed during movement, the order completes. If movement times out, the attack repeats.
        /// </summary>
        /// <param name="unit">The unit that will execute the attack order. Must have a valid UnitCommander and MovementController.</param>
        /// <param name="target">The target to attack. Must implement ITeamDamageable and not be destroyed.</param>
        /// <returns>A decorated order chain that handles movement to optimal position, gun targeting, and timeout handling.</returns>
        /// <remarks>
        /// Behavior:
        /// - Calculates optimal attack position based on gun range (70% of max range)
        /// - Uses TimedDecorator with 1.0 second timeout for movement
        /// - Applies GunTargetDecorator to set priority target during movement
        /// - On timeout, recursively creates new attack order if target still alive
        /// - Stopping distance: 0.5 world units for precise positioning
        ///
        /// Performance: Creates 2 decorators per call, uses pooled MoveOrder
        /// Completion: When target is destroyed or unit reaches optimal position and engages
        /// Interruption: Can be cancelled, will stop movement and clear gun target
        ///
        /// Example usage:
        /// var attackOrder = OrderUtils.CreateDirectAttackOrder(myUnit, enemyTarget);
        /// myUnit.unitCommander.IssueOrder(attackOrder);
        /// </remarks>
        public static IBaseOrder CreateDirectAttackOrder(UnitController unit, ITeamDamageable target)
        {
            // Validate parameters
            if (unit == null) throw new ArgumentNullException(nameof(unit));
            if (target == null) throw new ArgumentNullException(nameof(target));
            if (target.IsDestroyed) throw new ArgumentException("Target is already destroyed", nameof(target));

            // Calculate optimal attack position based on gun range
            var optimalPosition = CalculateOptimalAttackPosition(unit, target);
            var moveOrder = CreateMoveOrder(unit, optimalPosition, 0.5f); // Small stopping distance for positioning

            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            var timedMoveOrder = orderPoolManager?.CreateTimedDecorator(moveOrder) ?? new TimedDecorator(moveOrder);
            timedMoveOrder.timeLimit = 1.0f;
            timedMoveOrder.onTimeout += () =>
            {
                if (target.IsDestroyed)
                {
                    return;
                }

                // Repeat until target is dead
                var attackOrder = CreateDirectAttackOrder(unit, target);
                unit.unitCommander.IssueOrder(attackOrder);
            };

            var targetedTimedMoveOrder = orderPoolManager?.CreateGunTargetDecorator(timedMoveOrder) ??
                                         new GunTargetDecorator(timedMoveOrder);
            targetedTimedMoveOrder.target = target;
            return targetedTimedMoveOrder;
        }

        /// <summary>
        ///     Calculate optimal attack position that maintains proper distance from target for effective gun targeting
        /// </summary>
        private static Vector3 CalculateOptimalAttackPosition(UnitController unit, ITeamDamageable target)
        {
            if (unit.gunController?.gun == null)
            {
                // No gun, move directly to target
                return target.Position;
            }

            var gunRange = unit.gunController.gun.range;
            var currentDistance = Vector3.Distance(unit.Position, target.Position);

            // Define optimal distance range
            var minDistance = Mathf.Max(1.0f, gunRange * 0.3f); // Minimum distance to avoid getting too close
            var optimalDistance = gunRange * 0.7f; // Optimal distance for targeting

            // If already at good distance, stay put
            if (currentDistance >= minDistance && currentDistance <= optimalDistance)
            {
                return unit.Position;
            }

            // Calculate direction from target to unit
            var directionToUnit = (unit.Position - target.Position).normalized;

            // If unit is too close or direction is invalid, use a default direction
            if (directionToUnit.magnitude < 0.1f)
            {
                // Try different directions to find a good position
                var angles = new[] { 0f, 90f, 180f, 270f, 45f, 135f, 225f, 315f };
                foreach (var angle in angles)
                {
                    var direction = new Vector3(Mathf.Cos(angle * Mathf.Deg2Rad), Mathf.Sin(angle * Mathf.Deg2Rad), 0);
                    var testPosition = target.Position + (direction * optimalDistance);

                    // Use the first direction that doesn't overlap with the target
                    if (Vector3.Distance(testPosition, target.Position) >= minDistance)
                    {
                        directionToUnit = direction;
                        break;
                    }
                }

                // Fallback if no good direction found
                if (directionToUnit.magnitude < 0.1f)
                {
                    directionToUnit = Vector3.right;
                }
            }

            // Calculate optimal position
            var optimalPosition = target.Position + (directionToUnit * optimalDistance);

            return optimalPosition;
        }

        /// <summary>
        /// Creates an attack move order that moves to a position while detecting and engaging enemies along the way.
        /// The unit will move toward the target position, automatically detecting enemies within the specified radius,
        /// interrupting movement to attack detected enemies, then resuming movement after enemies are eliminated.
        /// </summary>
        /// <param name="unit">The unit that will execute the attack move order. Must have valid UnitCommander, MovementController, and GunController.</param>
        /// <param name="targetPosition">The final destination position in world coordinates.</param>
        /// <param name="detectionRadius">The radius in world units for enemy detection during movement. Default: 6.0 units. Range: 1.0-20.0 units recommended.</param>
        /// <returns>A pooled AttackMoveOrder configured with the specified parameters.</returns>
        /// <remarks>
        /// Behavior:
        /// - Moves toward target position using pathfinding
        /// - Continuously scans for enemies within detection radius every 0.2 seconds
        /// - Interrupts movement to attack detected enemies using optimal positioning
        /// - Resumes movement to original target after enemies are eliminated
        /// - Uses EnemyDetectionDecorator for continuous scanning
        ///
        /// Performance: Uses pooled AttackMoveOrder, creates decorators for enemy detection
        /// Completion: When unit reaches target position and no enemies are detected
        /// Interruption: Can be cancelled, will stop movement and combat
        ///
        /// Example usage:
        /// var attackMove = OrderUtils.CreateAttackMoveOrder(myUnit, destinationPos, 8f);
        /// myUnit.unitCommander.IssueOrder(attackMove);
        /// </remarks>
        public static IBaseOrder CreateAttackMoveOrder(UnitController unit, Vector3 targetPosition,
            float detectionRadius = 6f)
        {
            // Validate parameters
            if (unit == null) throw new ArgumentNullException(nameof(unit));
            if (detectionRadius <= 0)
                throw new ArgumentOutOfRangeException(nameof(detectionRadius), "Detection radius must be positive");

            var attackMoveOrder = AttackMoveOrder.Get();
            attackMoveOrder.Initialize(unit, targetPosition, detectionRadius, unit);
            return attackMoveOrder;
        }

        /// <summary>
        /// Creates a stay still order that stops all unit movement while allowing combat to continue.
        /// The unit will immediately stop moving and remain stationary until the order is cancelled.
        /// Gun targeting and combat functionality remain fully operational.
        /// </summary>
        /// <param name="unit">The unit that will execute the stay still order. Must have a valid MovementController.</param>
        /// <returns>A pooled StayStillOrder that stops movement but allows combat.</returns>
        /// <remarks>
        /// Behavior:
        /// - Immediately stops all movement via MovementController.StopMovement()
        /// - Does not interfere with gun targeting or combat systems
        /// - Order completes immediately but remains active until cancelled
        /// - Unit will not move even if enemies are detected or attacked
        ///
        /// Performance: Uses pooled StayStillOrder, minimal overhead
        /// Completion: Completes immediately but stays active (does not auto-advance order queue)
        /// Interruption: Can be cancelled to allow movement again
        ///
        /// Example usage:
        /// var stayOrder = OrderUtils.CreateStayStillOrder(myUnit);
        /// myUnit.unitCommander.IssueOrder(stayOrder);
        /// </remarks>
        public static IBaseOrder CreateStayStillOrder(UnitController unit)
        {
            // Validate parameters
            if (unit == null) throw new ArgumentNullException(nameof(unit));
            if (unit.movementController == null)
                throw new ArgumentException("Unit must have a MovementController", nameof(unit));

            var stayStillOrder = StayStillOrder.Get();
            stayStillOrder.Initialize(unit, unit.movementController);
            return stayStillOrder;
        }

        /// <summary>
        /// Creates a defend order that positions the unit at a specific location and attacks enemies within range.
        /// The unit will move to the defend position, then stay there while scanning for enemies within detection radius.
        /// Enemies will be engaged only if they are within the defend radius, ensuring the unit doesn't chase too far.
        /// </summary>
        /// <param name="unit">The unit that will execute the defend order. Must have valid UnitCommander, MovementController, and GunController.</param>
        /// <param name="defendPosition">The position in world coordinates where the unit should defend.</param>
        /// <param name="defendRadius">The maximum distance in world units from defend position that enemies can be engaged. Default: 5.0 units. Range: 1.0-15.0 units recommended.</param>
        /// <param name="detectionRadius">The radius in world units for enemy detection while defending. Default: 8.0 units. Should be >= defendRadius for optimal behavior.</param>
        /// <returns>A pooled DefendOrder configured with the specified parameters.</returns>
        /// <remarks>
        /// Behavior:
        /// - Moves to defend position using standard movement
        /// - Continuously scans for enemies within detection radius every 0.2 seconds
        /// - Engages enemies only if they are within defend radius from defend position
        /// - Uses ConditionalDecorator to enforce radius constraints during combat
        /// - Returns to defend position after combat if moved during engagement
        ///
        /// Performance: Uses pooled DefendOrder, creates decorators for detection and constraints
        /// Completion: Never completes automatically, must be cancelled
        /// Interruption: Can be cancelled, will stop defending and allow new orders
        ///
        /// Example usage:
        /// var defendOrder = OrderUtils.CreateDefendOrder(myUnit, chokePoint, 6f, 10f);
        /// myUnit.unitCommander.IssueOrder(defendOrder);
        /// </remarks>
        public static IBaseOrder CreateDefendOrder(UnitController unit, Vector3 defendPosition, float defendRadius = 5f,
            float detectionRadius = 8f)
        {
            // Validate parameters
            if (unit == null) throw new ArgumentNullException(nameof(unit));
            if (defendRadius <= 0)
                throw new ArgumentOutOfRangeException(nameof(defendRadius), "Defend radius must be positive");
            if (detectionRadius <= 0)
                throw new ArgumentOutOfRangeException(nameof(detectionRadius), "Detection radius must be positive");

            var defendOrder = DefendOrder.Get();
            defendOrder.Initialize(unit, defendPosition, defendRadius, detectionRadius, unit);
            return defendOrder;
        }

        /// <summary>
        /// Creates a random wander order that moves the unit to random positions within a specified radius.
        /// The unit will continuously move to random positions around the center point, creating patrol-like behavior.
        /// Each movement completes before selecting a new random destination, creating natural wandering patterns.
        /// </summary>
        /// <param name="unit">The unit that will execute the wander order. Must have valid UnitCommander and MovementController.</param>
        /// <param name="centerPosition">The center position in world coordinates around which the unit will wander.</param>
        /// <param name="wanderRadius">The maximum distance in world units from center position for random destinations. Range: 1.0-50.0 units recommended.</param>
        /// <returns>A move order that automatically chains to new random wander orders upon completion.</returns>
        /// <remarks>
        /// Behavior:
        /// - Selects random position within wander radius using Unity's Random.insideUnitCircle
        /// - Moves to selected position using standard movement with timeout
        /// - Upon reaching destination, automatically issues new random wander order
        /// - Creates infinite wandering behavior until cancelled
        /// - Uses recursive order chaining for continuous movement
        ///
        /// Performance: Creates new orders recursively, uses pooled MoveOrders with TimedDecorator
        /// Completion: Individual movements complete, but overall behavior is infinite
        /// Interruption: Can be cancelled to stop wandering, will not issue new wander orders
        ///
        /// Example usage:
        /// var wanderOrder = OrderUtils.CreateRandomWanderOrder(myUnit, basePosition, 15f);
        /// myUnit.unitCommander.IssueOrder(wanderOrder);
        /// </remarks>
        public static IBaseOrder CreateRandomWanderOrder(UnitController unit, Vector3 centerPosition,
            float wanderRadius)
        {
            // Validate parameters
            if (unit == null) throw new ArgumentNullException(nameof(unit));
            if (wanderRadius <= 0)
                throw new ArgumentOutOfRangeException(nameof(wanderRadius), "Wander radius must be positive");

            var random2Dposition = Random.insideUnitCircle * wanderRadius;
            var randomPosition = centerPosition + new Vector3(random2Dposition.x, random2Dposition.y, 0f);

            var moveOrder = CreateMoveOrder(unit, randomPosition);
            moveOrder.OnComplete += () =>
            {
                var randomWanderOrder = CreateRandomWanderOrder(unit, centerPosition, wanderRadius);
                unit.unitCommander.IssueOrder(randomWanderOrder);
            };

            return moveOrder;
        }

        /// <summary>
        /// Creates a move order to follow a dynamic target that can move during execution.
        /// Uses TargetTrackingDecorator to continuously update the target position every 0.2 seconds,
        /// ensuring the unit tracks moving targets effectively without creating new orders.
        /// </summary>
        /// <param name="unit">The unit that will execute the move order. Must have valid UnitCommander and MovementController.</param>
        /// <param name="target">The dynamic target to follow. Must implement ITeamDamageable and not be destroyed.</param>
        /// <param name="stoppingDistance">Additional stopping distance in world units from the target. Default: 0.0 units. Range: 0.0-10.0 units recommended.</param>
        /// <returns>A move order decorated with TargetTrackingDecorator for dynamic position updates.</returns>
        /// <remarks>
        /// Behavior:
        /// - Creates base move order with initial target position
        /// - Wraps with TargetTrackingDecorator for continuous position updates
        /// - Updates target position every 0.2 seconds if target moves more than 0.5 units
        /// - Automatically completes when unit reaches target within stopping distance
        /// - Handles target destruction gracefully by completing the order
        ///
        /// Performance: Uses pooled MoveOrder, creates TargetTrackingDecorator, updates every 0.2s
        /// Completion: When unit reaches target within stopping distance or target is destroyed
        /// Interruption: Can be cancelled, will stop movement and tracking
        ///
        /// Example usage:
        /// var followOrder = OrderUtils.CreateMoveToTargetOrder(myUnit, movingEnemy, 2f);
        /// myUnit.unitCommander.IssueOrder(followOrder);
        /// </remarks>
        public static IBaseOrder CreateMoveToTargetOrder(UnitController unit, ITeamDamageable target,
            float stoppingDistance = 0)
        {
            // Validate parameters
            if (unit == null) throw new ArgumentNullException(nameof(unit));
            if (target == null) throw new ArgumentNullException(nameof(target));
            if (target.IsDestroyed) throw new ArgumentException("Target is already destroyed", nameof(target));
            if (stoppingDistance < 0)
                throw new ArgumentOutOfRangeException(nameof(stoppingDistance), "Stopping distance cannot be negative");

            var totalOffset = MathUtils.CalculateOffset(unit);
            var adjustedStoppingDistance = totalOffset + stoppingDistance;

            // Create base move order with initial target position
            var moveOrder = MoveOrder.Get();
            moveOrder.Initialize(unit, target.Position, adjustedStoppingDistance, unit.movementController);

            // Wrap with target tracking decorator for dynamic position updates
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            var trackingDecorator = orderPoolManager?.CreateTargetTrackingDecorator(moveOrder) ??
                                    new TargetTrackingDecorator(moveOrder);
            trackingDecorator.target = target;
            trackingDecorator.stoppingDistance = adjustedStoppingDistance;
            trackingDecorator.updateInterval = 0.2f; // Update every 0.2 seconds

            return trackingDecorator;
        }

        /// <summary>
        /// Creates a move order to a static position with timeout protection.
        /// The unit will move to the specified position using pathfinding with a TimedDecorator
        /// to prevent getting stuck. The timeout is calculated based on distance and movement speed.
        /// </summary>
        /// <param name="unit">The unit that will execute the move order. Must have valid UnitCommander and MovementController.</param>
        /// <param name="targetPosition">The destination position in world coordinates.</param>
        /// <param name="stoppingDistance">Additional stopping distance in world units from the target position. Default: 0.0 units. Range: 0.0-10.0 units recommended.</param>
        /// <returns>A move order decorated with TimedDecorator for timeout protection.</returns>
        /// <remarks>
        /// Behavior:
        /// - Moves to static position using pathfinding
        /// - Uses TimedDecorator with timeout = (distance / speed) * 2 for safety margin
        /// - Position is not updated during movement (use CreateMoveToTargetOrder for dynamic targets)
        /// - Automatically completes when unit reaches position within stopping distance
        /// - Times out if movement takes longer than calculated time limit
        ///
        /// Performance: Uses pooled MoveOrder, creates TimedDecorator
        /// Completion: When unit reaches position within stopping distance or timeout occurs
        /// Interruption: Can be cancelled, will stop movement
        ///
        /// Example usage:
        /// var moveOrder = OrderUtils.CreateMoveOrder(myUnit, destinationPos, 1f);
        /// myUnit.unitCommander.IssueOrder(moveOrder);
        /// </remarks>
        public static IBaseOrder CreateMoveOrder(UnitController unit, Vector3 targetPosition,
            float stoppingDistance = 0)
        {
            // Validate parameters
            if (unit == null) throw new ArgumentNullException(nameof(unit));
            if (unit.movementController == null)
                throw new ArgumentException("Unit must have a MovementController", nameof(unit));
            if (stoppingDistance < 0)
                throw new ArgumentOutOfRangeException(nameof(stoppingDistance), "Stopping distance cannot be negative");

            var totalOffset = MathUtils.CalculateOffset(unit);

            var moveOrder = MoveOrder.Get();
            moveOrder.Initialize(unit, targetPosition, totalOffset + stoppingDistance, unit.movementController);

            var distance = Vector3.Distance(unit.transform.position, targetPosition);
            var travelTime = distance / unit.movementController.movementSpeed;

            // Ensure minimum timeout to prevent immediate timeouts for very short distances
            var timeLimit = Mathf.Max(1.0f, travelTime * 2f);
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            var timedMoveOrder = orderPoolManager?.CreateTimedDecorator(moveOrder) ?? new TimedDecorator(moveOrder);
            timedMoveOrder.timeLimit = timeLimit;

            return timedMoveOrder;
        }

        /// <summary>
        /// Creates a resource thief order that moves to a conveyor belt, steals resources, and returns to base.
        /// The unit will pathfind to the target conveyor, pause its operation, steal the resources with animation,
        /// then return to the specified base position. Includes retry logic for invalid targets.
        /// </summary>
        /// <param name="issuer">The object issuing the order (typically a spawner or manager). Used for order tracking.</param>
        /// <param name="targetConveyor">The conveyor belt to steal resources from. Must have resources available.</param>
        /// <param name="basePosition">The position in world coordinates to return to after stealing resources.</param>
        /// <param name="thiefUnit">The unit that will execute the theft. Must have valid UnitCommander and MovementController.</param>
        /// <returns>A pooled ResourceThiefOrder configured with the specified parameters.</returns>
        /// <remarks>
        /// Behavior:
        /// - Moves to target conveyor belt using pathfinding
        /// - Pauses conveyor operation during resource theft
        /// - Plays resource theft animation and destroys resources
        /// - Returns to base position after successful theft
        /// - Includes retry logic (up to 3 attempts) if target becomes invalid
        /// - Automatically finds alternative targets if original target is empty
        ///
        /// Performance: Uses pooled ResourceThiefOrder, moderate overhead due to conveyor interaction
        /// Completion: When unit returns to base position after successful theft or max retries reached
        /// Interruption: Can be cancelled, will resume conveyor operation and stop theft
        ///
        /// Example usage:
        /// var thiefOrder = OrderUtils.CreateResourceThiefOrder(spawner, targetBelt, basePos, thiefUnit);
        /// thiefUnit.unitCommander.IssueOrder(thiefOrder);
        /// </remarks>
        public static IBaseOrder CreateResourceThiefOrder(MonoBehaviour issuer, ConveyorBelt targetConveyor,
            Vector3 basePosition, UnitController thiefUnit)
        {
            // Validate parameters
            if (issuer == null) throw new ArgumentNullException(nameof(issuer));
            if (targetConveyor == null) throw new ArgumentNullException(nameof(targetConveyor));
            if (thiefUnit == null) throw new ArgumentNullException(nameof(thiefUnit));

            var thiefOrder = ResourceThiefOrder.Get();
            thiefOrder.Initialize(issuer, targetConveyor, basePosition, thiefUnit);
            return thiefOrder;
        }

        // Legacy method names for backward compatibility - these will be deprecated
        [Obsolete("Use CreateDirectAttackOrder instead", false)]
        public static IBaseOrder DirectAttackOrder(UnitController unit, ITeamDamageable target) =>
            CreateDirectAttackOrder(unit, target);

        [Obsolete("Use CreateAttackMoveOrder instead", false)]
        public static IBaseOrder AttackMove(UnitController unit, Vector3 targetPosition, float detectionRadius = 6f) =>
            CreateAttackMoveOrder(unit, targetPosition, detectionRadius);

        [Obsolete("Use CreateStayStillOrder instead", false)]
        public static IBaseOrder StayStill(UnitController unit) => CreateStayStillOrder(unit);

        [Obsolete("Use CreateDefendOrder instead", false)]
        public static IBaseOrder Defend(UnitController unit, Vector3 defendPosition, float defendRadius = 5f,
            float detectionRadius = 8f) => CreateDefendOrder(unit, defendPosition, defendRadius, detectionRadius);

        [Obsolete("Use CreateRandomWanderOrder instead", false)]
        public static IBaseOrder RandomWonder(UnitController unit, Vector3 targetPosition, float radius) =>
            CreateRandomWanderOrder(unit, targetPosition, radius);

        [Obsolete("Use CreateMoveToTargetOrder instead", false)]
        public static IBaseOrder MoveTo(UnitController unit, ITeamDamageable target, float stoppingDistance = 0) =>
            CreateMoveToTargetOrder(unit, target, stoppingDistance);

        [Obsolete("Use CreateMoveOrder instead", false)]
        public static IBaseOrder
            MoveToPosition(UnitController unit, Vector3 targetPosition, float stoppingDistance = 0) =>
            CreateMoveOrder(unit, targetPosition, stoppingDistance);
    }
}
