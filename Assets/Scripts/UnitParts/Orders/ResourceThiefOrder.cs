using Buildings;
using Common;
using Managers;
using System;
using System.Collections;
using UnitParts.Controllers;
using UnitParts.Interfaces;
using UnityEngine;

namespace UnitParts.Orders
{
    /// <summary>
    /// Specialized order for enemy units to steal resources from conveyor belts.
    /// The unit moves to a target conveyor belt, pauses its operation, steals the resources
    /// with visual animation, then returns to a base position. Includes retry logic for
    /// handling invalid targets and automatic fallback to alternative targets.
    /// </summary>
    /// <remarks>
    /// Behavior:
    /// - Moves to target conveyor belt using pathfinding with 1.5 unit stopping distance
    /// - Pauses conveyor operation during resource theft to prevent interference
    /// - Plays resource theft animation and destroys resources on contact
    /// - Returns to specified base position after successful theft
    /// - Includes retry logic (up to 3 attempts) if target becomes invalid during execution
    /// - Automatically finds alternative targets if original target is empty
    /// - Ensures conveyor operation resumes even if order is cancelled or fails
    ///
    /// Performance: Pooled object, moderate overhead due to conveyor interaction and animation
    /// Completion: When unit returns to base position after successful theft or max retries reached
    /// Interruption: Can be cancelled, will resume conveyor operation and stop theft process
    ///
    /// Usage Examples:
    /// - Basic theft: var theft = ResourceThiefOrder.Get(); theft.Initialize(spawner, targetBelt, basePos, thiefUnit);
    /// - Spawner integration: Used by EnemyWorkerSpawner to create resource harassment behavior
    /// - Economic warfare: Disrupts player resource production through targeted theft
    /// </remarks>
    [Serializable]
    public class ResourceThiefOrder : BaseOrder
    {
        private const int MAX_RETRIES = 3;
        public ConveyorBelt TargetConveyor;
        public Vector3 BasePosition;
        public UnitController UnitController;

        private bool _hasStolen;
        private bool _isReturningToBase;
        private int _retryCount;
        private Coroutine _stealingCoroutine;
        public IMovementController MovementController;

        public ResourceThiefOrder Initialize(MonoBehaviour issuer, ConveyorBelt targetConveyor, Vector3 basePosition,
            UnitController unitController, Action onComplete = null, Action onCancel = null)
        {
            base.Initialize(issuer, onComplete, onCancel);

            TargetConveyor = targetConveyor;
            BasePosition = basePosition;
            UnitController = unitController;
            MovementController = unitController.movementController;
            _hasStolen = false;
            _isReturningToBase = false;
            _retryCount = 0;

            return this;
        }

        public override void Dispose()
        {
            // Ensure conveyor is resumed before disposing
            CleanupConveyorState();

            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            orderPoolManager?.ResourceThiefOrderPool.Return(this);
        }

        /// <summary>
        ///     Cleanup conveyor state to ensure it resumes operation
        /// </summary>
        private void CleanupConveyorState()
        {
            if (TargetConveyor != null)
            {
                TargetConveyor.ResumeOperation();
            }
        }

        public static new ResourceThiefOrder Get()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            return orderPoolManager?.ResourceThiefOrderPool.Get();
        }

        public override void Execute()
        {
            if (TargetConveyor == null || TargetConveyor.CurrentlyHeldResource == null)
            {
                // Target is invalid, complete the order
                InvokeOnComplete();
                return;
            }

            // Move to the conveyor belt
            MovementController.MoveTo(TargetConveyor.transform.position, 1.5f, OnReachedConveyor);
        }

        private void OnReachedConveyor()
        {
            if (TargetConveyor == null || TargetConveyor.CurrentlyHeldResource == null)
            {
                // Target became invalid during movement, try to find a new target
                if (_retryCount < MAX_RETRIES)
                {
                    var newTarget = FindNearestActiveConveyorBelt(UnitController.Position);
                    if (newTarget != null && newTarget != TargetConveyor)
                    {
                        _retryCount++;
                        Debug.Log(
                            $"Original target empty, switching to new conveyor belt! (Retry {_retryCount}/{MAX_RETRIES})");
                        TargetConveyor = newTarget;
                        // Move to the new target
                        MovementController.MoveTo(TargetConveyor.transform.position, 1.5f, OnReachedConveyor);
                        return;
                    }
                }

                // No valid targets found or max retries reached, complete the order
                Debug.Log(_retryCount >= MAX_RETRIES
                    ? "Max retries reached, returning to base"
                    : "No valid targets found, returning to base");
                InvokeOnComplete();
                return;
            }

            // Start stealing process
            _stealingCoroutine = MovementController.StartCoroutine(StealResourceCoroutine());
        }

        private IEnumerator StealResourceCoroutine()
        {
            // Pause conveyor operation during theft
            TargetConveyor.PauseOperation();

            yield return new WaitForSeconds(0.2f);

            if (TargetConveyor != null && TargetConveyor.CurrentlyHeldResource != null)
            {
                var resource = TargetConveyor.CurrentlyHeldResource;

                // Clear the conveyor's resource reference BEFORE animation to prevent conflicts
                TargetConveyor.CurrentlyHeldResource = null;
                TargetConveyor.IsOccupied = false;

                // Animate resource moving to thief position
                yield return MovementController.StartCoroutine(AnimateResourceToThief(resource));

                // Steal the resource
                resource.Consume();
                _hasStolen = true;
                Debug.Log("Resource stolen from conveyor belt!");
            }

            // Resume conveyor operation
            TargetConveyor.ResumeOperation();

            // Return to base
            _isReturningToBase = true;
            MovementController.MoveTo(BasePosition, OnReturnedToBase);
        }

        private IEnumerator AnimateResourceToThief(IResource resource)
        {
            if (resource?.gameObject == null || UnitController == null)
            {
                yield break;
            }

            var startPosition = resource.gameObject.transform.position;
            var endPosition = UnitController.transform.position;
            var animationTime = 0.5f;
            var elapsedTime = 0f;

            while (elapsedTime < animationTime)
            {
                // Check if resource still exists
                if (resource?.gameObject == null)
                {
                    yield break;
                }

                try
                {
                    var t = elapsedTime / animationTime;
                    var easedT = Mathf.SmoothStep(0f, 1f, t);

                    resource.gameObject.transform.position = Vector3.Lerp(startPosition, endPosition, easedT);
                }
                catch (MissingReferenceException)
                {
                    // Resource was destroyed during animation
                    yield break;
                }

                elapsedTime += Time.deltaTime;
                yield return null;
            }

            // Ensure final position
            try
            {
                if (resource?.gameObject != null)
                {
                    resource.gameObject.transform.position = endPosition;
                }
            }
            catch (MissingReferenceException)
            {
                // Resource was destroyed, ignore
            }
        }

        private ConveyorBelt FindNearestActiveConveyorBelt(Vector3 fromPosition)
        {
            ConveyorBelt nearestConveyor = null;
            var nearestDistance = float.MaxValue;

            var buildingManager = ServiceLocator.Get<IBuildingManager>();
            if (buildingManager?.PlacedObjects == null)
            {
                return null;
            }

            foreach (var placedObject in buildingManager.PlacedObjects)
            {
                if (placedObject == null)
                {
                    continue;
                }

                var conveyor = placedObject.GetComponent<ConveyorBelt>();
                if (conveyor == null)
                {
                    continue;
                }

                // Check if conveyor belongs to enemy team (not the thief's team)
                if (UnitController != null && conveyor.Team == UnitController.Team)
                {
                    continue;
                }

                // Check if conveyor has resources
                if (conveyor.CurrentlyHeldResource == null)
                {
                    continue;
                }

                var distance = Vector3.Distance(fromPosition, conveyor.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestConveyor = conveyor;
                }
            }

            return nearestConveyor;
        }

        private void OnReturnedToBase() =>
            // Mission complete
            InvokeOnComplete();

        public override void Cancel()
        {
            if (_stealingCoroutine != null)
            {
                MovementController.StopCoroutine(_stealingCoroutine);
                _stealingCoroutine = null;
            }

            // Cleanup conveyor state
            CleanupConveyorState();

            MovementController.StopMovement();
            InvokeOnCancel();
        }
    }
}
