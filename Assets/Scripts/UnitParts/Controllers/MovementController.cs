using Managers;
using NaughtyAttributes;
using NavMeshCustom;
using System;
using UnitParts.Interfaces;
using UnityEngine;
using UnityEngine.AI;
using Random = UnityEngine.Random;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace UnitParts.Controllers
{
    [RequireComponent(typeof(Collider2D))]
    public class MovementController : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IMovementController
    {
        private const float StopingDistance = 0.1f;
        public float movementSpeed;
        public float angularSpeed;
        public float maxAngle = 45;

        [field: BoxGroup("Runtime variables")]
        [field: ReadOnly]
        [field: SerializeField]
        public float SteeringAngle { get; private set; }

        [field: BoxGroup("Runtime variables")]
        [field: ReadOnly]
        [field: SerializeField]
        public Vector3 TargetPosition { get; private set; }

        [field: BoxGroup("Runtime variables")]
        [field: ReadOnly]
        [field: SerializeField]
        public bool TargetSet { get; private set; }

        private NavMeshAgent _agent;

        private Vector3 _forwardVel;
        private Vector3 _lateralVel;

        private Action _onMoveComplete;
        public Vector2 velocity => _agent ? _agent.desiredVelocity : Vector2.zero;

        private void Update()
        {
            if (!_agent || !_agent.isOnNavMesh)
            {
                return;
            }

            if (TargetSet)
            {
                AgentUpdateTarget();
                TargetSet = false;
            }

            if (!_agent.isStopped)
            {
                var desired = _agent.desiredVelocity;
                var forward = transform.right;

                _agent.velocity = Vector3.zero;
                AlignToTarget(desired);

                SteeringAngle = Vector3.Angle(forward, desired);

                if (SteeringAngle > maxAngle)
                {
                    return;
                }

                var clampedSteeringAngle = Mathf.Clamp(SteeringAngle, 0, maxAngle);
                var movePower = (maxAngle - clampedSteeringAngle) / maxAngle;

                _forwardVel = Vector3.Project(desired, forward);
                _lateralVel = desired - _forwardVel;

                _lateralVel *= movePower * movePower;
                _forwardVel *= movePower;

                var newVelocity = _forwardVel + _lateralVel;

                _agent.velocity = newVelocity;
            }

            // Check if agent reached the destination
            if (!_agent.pathPending && _agent.remainingDistance <= _agent.stoppingDistance)
            {
                HandleMovementCompletion();
            }
        }

        private void OnDrawGizmosSelected()
        {
            var whiskersLenCoef = 2;

            Gizmos.color = Color.red;
            var minSpread = Quaternion.AngleAxis(maxAngle, Vector3.forward) * transform.right;
            var maxSpread = Quaternion.AngleAxis(-maxAngle, Vector3.forward) * transform.right;

            Gizmos.DrawLine(transform.position, transform.position + (minSpread * whiskersLenCoef));
            Gizmos.DrawLine(transform.position, transform.position + (maxSpread * whiskersLenCoef));
#if UNITY_EDITOR
            Handles.Label(transform.position, "Max Angle: " + maxAngle);
#endif

            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(transform.position, transform.position + (_forwardVel + _lateralVel));

            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(TargetPosition, StopingDistance);
#if UNITY_EDITOR
            Handles.Label(TargetPosition, "Target");
#endif
        }

        public void MoveTo(Vector3 targetPos, Action onMoveComplete = null)
        {
            Debug.Log("Moving to " + targetPos);

            _onMoveComplete = onMoveComplete;
            _agent.stoppingDistance = StopingDistance;

            TargetPosition = targetPos;
            TargetSet = true;
        }

        public void MoveTo(Vector3 targetPos, float stoppingDistance, Action onMoveComplete = null)
        {
            Debug.Log("Moving to " + targetPos);
            _onMoveComplete = onMoveComplete;
            _agent.stoppingDistance = stoppingDistance;

            TargetPosition = targetPos;
            TargetSet = true;
        }

        public void StopMovement()
        {
            TargetSet = false;
            AgentStop();
        }

        public void Init(UnitController controller)
        {
            _agent = gameObject.GetComponent<NavMeshAgent>();
            var unitCollider = gameObject.GetComponent<Collider2D>();

            // Prevent agent from rotating and updating up axis
            // Copypasta from AgentOverride2d.cs
            _agent.updateRotation = false;
            _agent.updateUpAxis = false;

            if (unitCollider)
            {
                _agent.radius = unitCollider.bounds.extents.x / transform.localScale.x;
            }
            else
            {
                Debug.LogWarning("No Collider2D found");
                _agent.radius = gameObject.transform.localScale.x / transform.localScale.x;
            }

            _agent.radius += 0.01f;

            _agent.stoppingDistance = StopingDistance;
            _agent.avoidancePriority = Random.Range(0, 100);
            _agent.agentTypeID = NavMeshManager.GetAgentTypeBasedOnTeam(controller.Team).GetAgentTypeID();

            _agent.speed = movementSpeed;
            _agent.acceleration = movementSpeed * 4;
        }

        private void AlignToTarget(Vector3 desiredDirection)
        {
            var angle = Vector2.SignedAngle(Vector2.right, desiredDirection);
            var targetRotation = Quaternion.Euler(0, 0, angle);

            // Calculate the difference in rotation
            var deltaRotation = targetRotation * Quaternion.Inverse(transform.rotation);
            var angleDifference = Mathf.Abs(deltaRotation.eulerAngles.z);
            var maxChange = angularSpeed * Time.deltaTime;
            if (angleDifference < 1f)
            {
                transform.rotation =
                    Quaternion.RotateTowards(transform.rotation, targetRotation, maxChange);
            }
            else
            {
                transform.rotation = Quaternion.Lerp(transform.rotation, targetRotation, maxChange);
            }
        }

        private void ActionDone()
        {
            StopMovement();

            var callback = _onMoveComplete;
            _onMoveComplete = null;
            callback?.Invoke();
        }

        /// <summary>
        /// Handles movement completion detection with robust NavMesh rebuild handling.
        /// Prevents premature completion when NavMesh rebuilds invalidate paths.
        /// </summary>
        private void HandleMovementCompletion()
        {
            // Check path status to distinguish between legitimate completion and NavMesh rebuild issues
            switch (_agent.pathStatus)
            {
                case NavMeshPathStatus.PathComplete:
                    ActionDone();
                    break;

                case NavMeshPathStatus.PathPartial:
                    ActionDone();
                    break;

                case NavMeshPathStatus.PathInvalid:
                    TriggerRecalculatePath();
                    break;
            }
        }

        /// <summary>
        /// Recalculates the path to the original target position.
        /// Used when NavMesh rebuilds invalidate the current path.
        /// </summary>
        private void TriggerRecalculatePath()
        {
            if (!TargetSet)
            {
                return;
            }

            // Recalculate path to original target
            TargetSet = true;
            Debug.Log($"Recalculating path to {TargetPosition} due to NavMesh changes");
        }

        private void AgentUpdateTarget()
        {
            _agent.SetDestination(TargetPosition);
            _agent.isStopped = false;
        }

        private void AgentStop()
        {
            if (_agent && _agent.isOnNavMesh && !_agent.isStopped)
            {
                _agent.isStopped = true;
            }
        }
    }
}
