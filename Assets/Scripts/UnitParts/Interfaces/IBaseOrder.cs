using System;

namespace UnitParts.Interfaces
{
    public interface IBaseOrder : IDisposable
    {
        /// <summary>
        ///     Who is issuing the order
        ///     Manages coroutines and other order related stuff
        /// </summary>
        object Issuer { get; }

        /// <summary>
        ///     Called when the order is completed
        /// </summary>
        Action OnComplete { get; set; }

        /// <summary>
        ///     Called when the order is cancelled
        /// </summary>
        Action OnCancel { get; set; }

        /// <summary>
        ///     Called when the order is executed
        ///     (called by order manager)
        /// </summary>
        void Execute();

        /// <summary>
        ///     Called when the order is cancelled
        ///     (called by order manager)
        /// </summary>
        void Cancel();
    }

    /// <summary>
    /// Interface for decorators that can be pooled and reused.
    /// Extends IBaseOrder with initialization and reset capabilities.
    /// </summary>
    public interface IPoolableDecorator : IBaseOrder
    {
        /// <summary>
        /// Initializes the decorator with a wrapped order and resets all state.
        /// This method should be called when getting a decorator from the pool.
        /// </summary>
        /// <param name="wrappedOrder">The order to wrap with this decorator</param>
        void Initialize(IBaseOrder wrappedOrder);

        /// <summary>
        /// Resets the decorator to its initial state for reuse.
        /// This method should clear all state and prepare the decorator for pooling.
        /// </summary>
        void Reset();
    }
}
