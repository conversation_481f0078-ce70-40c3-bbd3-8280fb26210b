using NaughtyAttributes;
using System.Collections.Generic;
using System.Linq;
using UnitParts.Interfaces;
using UnityEngine;

namespace Components
{
    /// <summary>
    ///     Simple damage handler for bars.
    ///     Splits damage between all registered bars.
    /// </summary>
    [RequireComponent(typeof(Rigidbody2D))]
    [RequireComponent(typeof(Collider2D))]
    public class DamageHandlerComponent : MonoBehaviour, IDamageableComponent
    {
        private readonly List<IDamageableModule> _bars = new();
        public IDamageableModule CurrentBar => _bars.First(bar => bar.CurrentValue > 0);

        public void Start()
        {
            if (_bars.Count == 0)
            {
                Debug.LogError("BarDamageHandler has no bars registered");
            }
        }

        private void OnEnable() =>
            AfterDamageOverFlowCallback += remainingDamage =>
            {
                Destroy(gameObject);
            };

        private void OnDisable() =>
            AfterDamageOverFlowCallback -= remainingDamage =>
            {
                Destroy(gameObject);
            };

        protected void OnDestroy() => OnDestroyCallback?.Invoke();

        public OnDestroyCallback OnDestroyCallback { get; set; }

        public AfterCompleteOverFlowCallback AfterDamageOverFlowCallback { get; set; }

        public AfterCompleteOverFlowCallback AfterHealOverFlowCallback { get; set; }

        public float TakeDamage(float amount)
        {
            var remainingDamage = amount;
            foreach (var bar in _bars)
            {
                if (bar.CurrentValue > 0)
                {
                    remainingDamage = bar.TakeDamage(remainingDamage);
                    if (remainingDamage <= 0)
                    {
                        break;
                    }
                }
            }

            if (remainingDamage > 0)
            {
                AfterDamageOverFlowCallback?.Invoke(remainingDamage);
            }

            if (remainingDamage > 0 || IsDestroyed)
            {
                OnDestroyCallback?.Invoke();
                OnDestroyCallback = null;

                Destroy(gameObject);
            }

            return remainingDamage;
        }

        public float TakeHeal(float amount)
        {
            var remainingHeal = amount;
            foreach (var bar in _bars)
            {
                if (bar.CurrentValue < bar.MaxValue)
                {
                    remainingHeal = bar.TakeHeal(remainingHeal);
                    if (remainingHeal <= 0)
                    {
                        break;
                    }
                }
            }

            if (remainingHeal > 0)
            {
                AfterHealOverFlowCallback?.Invoke(remainingHeal);
            }

            return remainingHeal;
        }

        public bool IsDestroyed
        {
            get
            {
                foreach (var bar in _bars)
                {
                    if (!bar.IsDestroyed)
                    {
                        return false;
                    }
                }

                return true;
            }
        }

        public float CurrentValue
        {
            get
            {
                var currentValue = 0f;
                foreach (var bar in _bars)
                {
                    currentValue += bar.CurrentValue;
                }

                return currentValue;
            }
        }

        public float MaxValue
        {
            get
            {
                var maxValue = 0f;
                foreach (var bar in _bars)
                {
                    maxValue += bar.MaxValue;
                }

                return maxValue;
            }
        }

        public bool WouldUnderFlow(float amount)
        {
            amount = _bars.Aggregate(amount, (current, bar) => current - bar.CurrentValue);

            return amount <= 0;
        }

        /// <summary>
        ///     Register a bar to be used for damage handling.
        /// </summary>
        /// <param name="bar">Bar to register</param>
        public void RegisterBar(IDamageableModule bar)
        {
            _bars.Add(bar);
            _bars.Sort((a, b) => b.ModulePriority.CompareTo(a.ModulePriority)); // Higher priority first
        }

        /// <summary>
        ///     Unregister a bar from the damage handler.
        /// </summary>
        /// <param name="bar">Bar to unregister</param>
        public void UnregisterBar(IDamageableModule bar) => _bars.Remove(bar);

        [Button("Take Damage")]
        // ReSharper disable once UnusedMember.Global
        public void DebugDamage()
        {
            var damage = Mathf.Min(CurrentBar.CurrentValue, CurrentBar.MaxValue / 2f) + 0.1f;
            Debug.Log($"Taking {damage} damage to {CurrentBar} ({CurrentBar.CurrentValue}/{CurrentBar.MaxValue})");
            TakeDamage(damage);
        }
    }
}
